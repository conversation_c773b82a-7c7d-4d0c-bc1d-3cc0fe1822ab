// Model3DComponent.tsx
import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Button,
  Tooltip,
  Input,
  Switch,
  Slider,
  Modal,
  Radio,
  Select,
  Progress,
  message,
} from 'antd';
import type { RadioChangeEvent } from 'antd';
import { EngineContainer } from '../../../../engines';
import '../../MediaComponentStyles.css';
import { IEdTechRenderProps } from '../../../../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../../../../hocs/withEdComponentParams/withEdComponentParams';
import { ITextProps } from '../../../../../core/title/CoreTitle';

// Import shared components
import {
  MediaFileUploaderContainer,
  UploadedFile,
} from '../../shared/MediaFileUploader';
import { DragDropMediaWrapper } from '../../shared';
import { Model3DComponentProps, Model3DItem } from '../shared/type';
import { defaultProps } from '../shared/constants';
import R3FModel3DViewer, { R3FModel3DViewerRef } from './R3FModel3DViewer';
import tempFileApi from '../../../../../../api/tempFileApi';
import {
  processModelUrl,
  guessFormatFromUrl,
  getFileNameWithoutExtension,
  getFileExtension,
  process3DFilesToBase64JS,
} from '../../shared/utils';
import {
  ArrowDownloadIcon,
  CheckmarkOutlineIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  DeleteIcon,
  DismissOutlineIcon,
  EditIcon,
  LinkOutlinedIcon,
  FileOutlineIcon,
} from '../../../../../icons/IconIndex';

const Model3DComponent: React.FC<IEdTechRenderProps<Model3DComponentProps>> = (
  props
) => {
  const { params, isEditing, addOrUpdateParamComponent } = props;

  // Merge with default props
  const config: Model3DComponentProps = { ...defaultProps, ...params };

  // Component state
  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const [tempUrl, setTempUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [loadProgress, setLoadProgress] = useState<number>(0);
  const [editingNameIndex, setEditingNameIndex] = useState<number | null>(null);
  const [tempModelName, setTempModelName] = useState<string>('');

  // State for tracking loading progress of each model in vertical mode
  const [modelLoadingStates, setModelLoadingStates] = useState<{
    [key: number]: { loading: boolean; progress: number };
  }>({});

  // Temporary settings state
  const [tempSettings, setTempSettings] = useState({
    autoRotate: config.autoRotate,
    rotationSpeed: config.rotationSpeed || 1,
  });

  // State for auto-convert to base64 JS
  const [autoConvertToBase64JS, setAutoConvertToBase64JS] =
    useState<boolean>(false);

  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const modelViewerRef = useRef<R3FModel3DViewerRef>(null);

  // Get current model
  const currentModel = React.useMemo(() => {
    if (config.medias && config.medias.length > 0) {
      return config.medias[selectedIndex];
    } else if (config.mediaUrl) {
      // Legacy support
      return {
        mediaUrl: config.mediaUrl,
        mediaType: config.mediaType,
        format: config.format,
      } as Model3DItem;
    }
    return null;
  }, [config.mediaUrl, config.mediaType, config.medias, selectedIndex]);

  // Track previous URL and format to prevent duplicate loading
  const prevUrlRef = useRef<string>('');
  const prevFormatRef = useRef<string>('');

  // Load model only when model URL or format changes, not on other config changes
  useEffect(() => {
    if (currentModel && currentModel.mediaUrl) {
      const currentUrl = currentModel.mediaUrl;
      const currentFormat = currentModel.format || '';

      // Only set loading to true if the URL or format has actually changed
      if (
        currentUrl !== prevUrlRef.current ||
        currentFormat !== prevFormatRef.current
      ) {
        // Reset progress to 0 when loading a new model
        setLoadProgress(0);
        setIsLoading(true);
        prevUrlRef.current = currentUrl;
        prevFormatRef.current = currentFormat;
      }
    }
  }, [currentModel?.mediaUrl, currentModel?.format]);

  // Update settings when config changes
  useEffect(() => {
    setTempSettings({
      autoRotate: config.autoRotate || false,
      rotationSpeed: config.rotationSpeed || 1,
    });
  }, [config.autoRotate, config.rotationSpeed]);

  // Handle model loading completion
  const handleModelLoad = useCallback(() => {
    // Đảm bảo tiến độ đạt 100% trước khi ẩn loading
    setLoadProgress(100);

    // Thêm một độ trễ nhỏ để người dùng thấy tiến độ đạt 100%
    setTimeout(() => {
      setIsLoading(false);

      // Reset view sau khi đã ẩn loading
      if (modelViewerRef.current) {
        modelViewerRef.current.resetView();
      }
    }, 300);
  }, []);

  // Handle model loading error
  const handleModelError = useCallback(() => {
    setIsLoading(false);
    setLoadProgress(0);
  }, []);

  // Handle model loading progress
  const handleModelProgress = useCallback((progress: number) => {
    setLoadProgress((prevProgress) => {
      if (progress === 0) return 0;

      if (progress === 100) return 100;

      return progress > prevProgress ? progress : prevProgress;
    });
  }, []);

  // Handle model loading progress for a specific model in vertical mode
  const handleModelProgressForIndex = useCallback(
    (index: number, progress: number) => {
      setModelLoadingStates((prev) => {
        const newState = { ...prev };
        newState[index] = {
          loading: progress < 100,
          progress: progress,
        };
        return newState;
      });
    },
    []
  );

  // Handle model load complete for a specific model in vertical mode
  const handleModelLoadForIndex = useCallback((index: number) => {
    setModelLoadingStates((prev) => {
      const newState = { ...prev };
      newState[index] = {
        loading: false,
        progress: 100,
      };
      return newState;
    });
  }, []);

  // Handle model load error for a specific model in vertical mode
  const handleModelErrorForIndex = useCallback((index: number) => {
    setModelLoadingStates((prev) => {
      const newState = { ...prev };
      newState[index] = {
        loading: false,
        progress: 0,
      };
      return newState;
    });
  }, []);

  // Toggle auto-rotation
  const toggleAutoRotate = () => {
    const newValue = !config.autoRotate;

    // Update component params
    addOrUpdateParamComponent({
      ...config,
      autoRotate: newValue,
    });

    // Also update the 3D viewer
    if (modelViewerRef.current) {
      modelViewerRef.current.toggleAutoRotate();
    }
  };

  // Ensure we have a models array
  useEffect(() => {
    // If we have a legacy modelUrl but no models array, convert it
    if (
      config.mediaUrl &&
      config.blobKey &&
      config.blobContext &&
      (!config.medias || config.medias.length === 0)
    ) {
      addOrUpdateParamComponent({
        ...config,
        medias: [
          {
            blobKey: config.blobKey,
            blobContext: config.blobContext,
            name: config.name || 'Mô hình 3D',
            mediaUrl: config.mediaUrl,
            mediaType: config.mediaType || 'upload',
            format: config.format || 'glb',
          },
        ],
      });
    }
  }, [config.mediaUrl, config.medias]);

  // Initialize loading states for all models when models array changes
  useEffect(() => {
    if (config.medias && config.medias.length > 0) {
      const initialStates: {
        [key: number]: { loading: boolean; progress: number };
      } = {};

      config.medias.forEach((model, index) => {
        if (model.mediaUrl) {
          initialStates[index] = {
            loading: true,
            progress: 0,
          };
        }
      });

      setModelLoadingStates(initialStates);
    }
  }, [config.medias]);

  // Handle URL submission
  const handleUrlSubmit = () => {
    if (!tempUrl) return;

    // Try to determine format from URL using our utility function
    const format = guessFormatFromUrl(tempUrl);

    if (format) {
      // Format determined, add the model
      addModelWithFormat(format);
    } else {
      // If can't determine, show a format selection dialog
      Modal.confirm({
        title: 'Chọn định dạng mô hình 3D',
        content: (
          <div>
            <p>
              Không thể xác định định dạng mô hình từ URL. Vui lòng chọn định
              dạng:
            </p>
            <Radio.Group
              onChange={(e: RadioChangeEvent) => {
                const selectedFormat = e.target.value as Model3DItem['format'];
                addModelWithFormat(selectedFormat);
              }}
              defaultValue="glb"
              style={{ marginTop: '10px' }}
            >
              <Radio value="glb">GLB</Radio>
              <Radio value="obj">OBJ</Radio>
              <Radio value="fbx">FBX</Radio>
            </Radio.Group>
          </div>
        ),
        okText: 'Thêm mô hình',
        cancelText: 'Hủy',
      });
    }
  };

  // Helper function to add a model with a specific format
  const addModelWithFormat = (format: Model3DItem['format']) => {
    if (!tempUrl) return;

    // Create new model item with descriptive name
    const newModel: Model3DItem = {
      mediaUrl: tempUrl,
      mediaType: 'embed',
      format: format,
      name: `Mô hình từ URL (${format?.toUpperCase() || 'GLB'})`,
      blobContext: '',
      blobKey: '',
    };

    // Add to models array
    const newModels = config.medias ? [...config.medias, newModel] : [newModel];

    // Update component
    addOrUpdateParamComponent({
      ...config,
      medias: newModels,
    });

    // Clear URL input
    setTempUrl('');
  };

  // Using utility functions from urlUtils.ts for format detection and URL processing

  // Handle model upload success
  const handleUploadSuccess = (files: UploadedFile[]) => {
    if (!files.length) return;

    // Create new model items from uploaded files
    const newUploadedModels = files.map((item) => {
      // Use our utility function to determine format from file name
      const format = guessFormatFromUrl(item.name || '') || 'glb'; // Default to glb if can't determine
      const { fullUrl, originalUrl } = processModelUrl(item.url);

      return {
        mediaUrl: fullUrl,
        originalUrl: originalUrl,
        mediaType: 'upload' as const,
        format: format,
        name: item.name || 'Mô hình 3D',
        blobContext: item.blobContext || '',
        blobKey: item.blobKey || '',
      } as Model3DItem;
    });

    // Combine with existing models
    const updatedModels = config.medias
      ? [...config.medias, ...newUploadedModels]
      : newUploadedModels;

    // Update component
    addOrUpdateParamComponent({
      ...config,
      medias: updatedModels,
    });
  };

  // Helper function to delete a file from the server
  const deleteModelFile = async (model: Model3DItem) => {
    const key = model.blobKey;
    if (key) {
      try {
        await tempFileApi.Delete(key);
      } catch (error) {
        // Silent error - continue with UI update even if server delete fails
      }
    }
  };

  // Handle deleting a model
  const handleDeleteModel = async (index?: number) => {
    // If no index is provided, delete all models
    if (index === undefined) {
      if (!config.medias || config.medias.length === 0) {
        return;
      }

      // Delete all uploaded models from server
      const deletePromises = config.medias
        .filter((model) => model.mediaType === 'upload')
        .map((model) => deleteModelFile(model));

      await Promise.allSettled(deletePromises);

      // Update component
      addOrUpdateParamComponent({
        ...config,
        medias: [],
      });

      setSelectedIndex(0);
    } else {
      // Delete specific model
      if (!config.medias || index >= config.medias.length) {
        return;
      }

      const modelToDelete = config.medias[index];

      // Delete from server if it's an upload
      if (modelToDelete.mediaType === 'upload') {
        await deleteModelFile(modelToDelete);
      }

      // Create new models array without the deleted item
      const newModels = [...config.medias];
      newModels.splice(index, 1);

      // Update selected index if needed
      if (selectedIndex === index) {
        if (newModels.length > 0) {
          // Select previous model, or first if deleting first model
          setSelectedIndex(index > 0 ? index - 1 : 0);
        } else {
          setSelectedIndex(0);
        }
      } else if (selectedIndex > index) {
        // Selected model is after the deleted one, adjust index
        setSelectedIndex(selectedIndex - 1);
      }

      // Update component
      addOrUpdateParamComponent({
        ...config,
        medias: newModels,
      });
    }
  };

  // Handle model selection
  const handleSelectModel = (index: number) => {
    if (!config.medias || index >= config.medias.length) return;
    setSelectedIndex(index);
  };

  // Handle editing model name
  const startEditingModelName = (index: number) => {
    if (!config.medias || index >= config.medias.length) return;

    const currentName = config.medias[index].name || '';
    // Chỉ lấy tên file mà không lấy phần extension
    const nameWithoutExtension = getFileNameWithoutExtension(currentName);
    setTempModelName(nameWithoutExtension);
    setEditingNameIndex(index);
  };

  // Save edited model name
  const saveModelName = () => {
    if (
      editingNameIndex === null ||
      !config.medias ||
      editingNameIndex >= config.medias.length
    )
      return;

    const currentName = config.medias[editingNameIndex].name || '';
    // Lấy phần extension từ tên hiện tại
    const extension = getFileExtension(currentName);
    // Tạo tên mới bằng cách kết hợp tên đã chỉnh sửa với extension cũ
    const newName = tempModelName + (extension ? `.${extension}` : '');

    const newModels = [...config.medias];
    newModels[editingNameIndex] = {
      ...newModels[editingNameIndex],
      name: newName,
    };

    addOrUpdateParamComponent({
      ...config,
      medias: newModels,
    });

    setEditingNameIndex(null);
    setTempModelName('');
  };

  // Cancel editing model name
  const cancelEditingModelName = () => {
    setEditingNameIndex(null);
    setTempModelName('');
  };

  // Handle downloading the 3D model
  const handleDownloadModel = (model: Model3DItem) => {
    console.log('Downloading model:', model);
    if (!model || !model.mediaUrl) {
      message.error('Không thể tải xuống mô hình này');
      return;
    }

    try {
      // For uploaded files with blobKey, use the API to get the file
      if (model.mediaType === 'upload' && model.blobKey) {
        // Show loading message
        const loadingMessage = message.loading('Đang tải xuống...', 0);
        fetch(model.mediaUrl)
          .then((response) => {
            if (!response.ok) {
              throw new Error('Network response was not ok');
            }
            return response.blob();
          })
          .then((blob) => {
            // Create a download link
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;

            // Use the model name if available, or create a default name with the correct extension
            const extension = model.format || 'glb';
            const fileName = model.name || `model-3d.${extension}`;
            link.setAttribute('download', fileName);

            // Trigger the download
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up the URL object
            window.URL.revokeObjectURL(url);

            // Close the loading message
            loadingMessage();

            // Show success message
          })
          .catch((error) => {
            console.error('Error downloading file:', error);
            loadingMessage();
            message.error('Không thể tải xuống mô hình. Vui lòng thử lại sau.');
          });
      }
      // For embedded URLs, just open the URL in a new tab
      else if (model.mediaType === 'embed') {
        window.open(model.mediaUrl, '_blank');
      }
    } catch (error) {
      console.error('Error downloading model:', error);
      message.error('Không thể tải xuống mô hình. Vui lòng thử lại sau.');
    }
  };

  // Handle converting 3D model to base64 JS file
  const handleConvertToBase64JS = async (model: Model3DItem) => {
    console.log('Converting model to base64 JS:', model);
    if (!model || !model.mediaUrl) {
      message.error('Không thể convert mô hình này');
      return;
    }

    try {
      // For uploaded files with blobKey, fetch the file and convert
      if (model.mediaType === 'upload' && model.blobKey) {
        // Show loading message
        const loadingMessage = message.loading(
          'Đang convert thành base64 JS...',
          0
        );

        fetch(model.mediaUrl)
          .then((response) => {
            if (!response.ok) {
              throw new Error('Network response was not ok');
            }
            return response.blob();
          })
          .then(async (blob) => {
            // Convert blob to File object
            const extension = model.format || 'glb';
            const fileName = model.name || `model-3d.${extension}`;
            const file = new File([blob], fileName, { type: blob.type });

            // Process the file to base64 JS
            await process3DFilesToBase64JS([file]);

            // Close the loading message
            loadingMessage();

            // Show success message
            message.success('Đã tạo file base64 JS thành công!');
          })
          .catch((error) => {
            console.error('Error converting file:', error);
            loadingMessage();
            message.error('Không thể convert mô hình. Vui lòng thử lại sau.');
          });
      }
      // For embedded URLs, show error as we can't convert external files
      else if (model.mediaType === 'embed') {
        message.error(
          'Không thể convert file từ URL bên ngoài. Chỉ có thể convert file đã upload.'
        );
      }
    } catch (error) {
      console.error('Error converting model:', error);
      message.error('Không thể convert mô hình. Vui lòng thử lại sau.');
    }
  };

  // Apply settings changes immediately when they change
  useEffect(() => {
    // Only update if the component is mounted and editing
    if (isEditing && tempSettings) {
      addOrUpdateParamComponent({
        ...config,
        autoRotate: tempSettings.autoRotate,
        rotationSpeed: tempSettings.rotationSpeed,
      });
    }
  }, [tempSettings, isEditing]);

  // Handle title updates
  const handleTitleUpdate = (titleProps: Partial<ITextProps>) => {
    if (addOrUpdateParamComponent) {
      const updatedConfig = {
        ...config,
        titleProps: {
          ...config.titleProps,
          ...titleProps,
        },
      };
      addOrUpdateParamComponent(updatedConfig);
    }
  };

  // Render model thumbnail item
  const renderModelThumbnailItem = (model: Model3DItem, index: number) => {
    return (
      <div
        key={index}
        className={`tailwind-cursor-pointer tailwind-rounded-lg tailwind-p-3 tailwind-transition-all tailwind-duration-200 ${
          index === selectedIndex
            ? 'tailwind-bg-primary-100 tailwind-border-primary tailwind-border-2 tailwind-shadow-md'
            : 'tailwind-bg-white tailwind-border tailwind-border-gray-200 hover:tailwind-border-primary hover:tailwind-shadow-sm'
        }`}
        onClick={() => handleSelectModel(index)}
        style={{
          minWidth: '150px',
          maxWidth: '200px',
        }}
      >
        <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-gap-2">
          {/* Format badge with improved styling */}
          <div className="tailwind-bg-gray-700 tailwind-text-white tailwind-px-3 tailwind-py-1 tailwind-rounded-full tailwind-text-xs tailwind-uppercase tailwind-font-semibold">
            {model.format || 'Mô hình 3D'}
          </div>
          {/* Model name with better typography */}
          <div className="tailwind-text-center tailwind-text-sm tailwind-font-medium tailwind-truncate tailwind-w-full">
            {getFileNameWithoutExtension(model.name || `Mô hình ${index + 1}`)}
          </div>
          {/* Type indicator */}
          <div className="tailwind-text-xs tailwind-text-gray-500">
            {model.mediaType === 'upload' ? 'Tệp đã tải lên' : 'Liên kết'}
          </div>
        </div>
      </div>
    );
  };

  // Render a single model item for vertical display mode
  const renderModelItem = (
    model: Model3DItem,
    index: number,
    isVertical: boolean = false
  ) => {
    // Get model name to display
    const modelName = model.name || `Mô hình ${index + 1}`;

    return (
      <div
        key={index}
        className={`tailwind-w-full tailwind-relative ${
          isVertical ? (isEditing ? 'tailwind-mb-6' : 'tailwind-mb-4') : ''
        }`}
      >
        {/* Model name and controls row */}
        <div
          className={`tailwind-flex tailwind-justify-between tailwind-items-center tailwind-w-full tailwind-mb-2`}
        >
          {/* Model name with edit functionality */}
          <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
            {editingNameIndex === index ? (
              <div className="tailwind-flex tailwind-items-center tailwind-gap-1">
                <Input
                  value={tempModelName}
                  onChange={(e) => setTempModelName(e.target.value)}
                  size="small"
                  className="tailwind-max-w-[200px]"
                  autoFocus
                  onPressEnter={saveModelName}
                />
                <Button
                  type="text"
                  size="small"
                  icon={<CheckmarkOutlineIcon />}
                  onClick={saveModelName}
                  style={{ color: '#52c41a' }}
                />
                <Button
                  type="text"
                  size="small"
                  icon={<DismissOutlineIcon />}
                  onClick={cancelEditingModelName}
                  style={{ color: '#ff4d4f' }}
                />
              </div>
            ) : (
              <div className="tailwind-flex tailwind-items-center tailwind-gap-1">
                <span className="tailwind-text-sm tailwind-font-medium">
                  {getFileNameWithoutExtension(modelName)}
                </span>
                {isEditing && (
                  <Tooltip title="Chỉnh sửa tên mô hình">
                    <Button
                      type="text"
                      icon={<EditIcon height={14} width={14} />}
                      onClick={() => startEditingModelName(index)}
                      className="tailwind-text-gray-500 hover:tailwind-text-primary"
                      style={{
                        width: '26px',
                        height: '26px',
                        minWidth: '26px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: 0,
                      }}
                    />
                  </Tooltip>
                )}
              </div>
            )}
          </div>

          {/* Control buttons */}
          <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
            {/* Download button - always visible */}
            <Tooltip title="Tải xuống mô hình">
              <Button
                type="text"
                icon={<ArrowDownloadIcon />}
                onClick={(e) => {
                  if (e) e.stopPropagation();
                  handleDownloadModel(model);
                }}
                style={{
                  width: '26px',
                  height: '26px',
                  minWidth: '26px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: 0,
                  backgroundColor: 'white',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px',
                }}
              />
            </Tooltip>

            {/* Convert to Base64 JS button - only for uploaded files */}
            {model.mediaType === 'upload' && model.blobKey && (
              <Tooltip title="Convert thành file base64 JS">
                <Button
                  type="text"
                  icon={<FileOutlineIcon />}
                  onClick={(e) => {
                    if (e) e.stopPropagation();
                    handleConvertToBase64JS(model);
                  }}
                  style={{
                    width: '26px',
                    height: '26px',
                    minWidth: '26px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: 0,
                    backgroundColor: 'white',
                    border: '1px solid #1890ff',
                    borderRadius: '4px',
                    color: '#1890ff',
                  }}
                />
              </Tooltip>
            )}

            {/* Delete button - only in edit mode */}
            {isEditing && (
              <Tooltip title="Xóa mô hình">
                <Button
                  type="text"
                  danger
                  icon={<DeleteIcon />}
                  onClick={(e) => {
                    if (e) e.stopPropagation();
                    handleDeleteModel(index);
                  }}
                  style={{
                    width: '26px',
                    height: '26px',
                    minWidth: '26px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: 0,
                    backgroundColor: 'white',
                    border: '1px solid #ff4d4f',
                    borderRadius: '4px',
                  }}
                />
              </Tooltip>
            )}
          </div>
        </div>

        {isVertical && (
          // Use the same grid layout as horizontal mode for consistent sizing
          <div className="tailwind-w-full tailwind-grid tailwind-grid-cols-[60px_1fr_60px] tailwind-gap-2 tailwind-items-center">
            {/* Left empty space for consistent layout */}
            <div className="tailwind-flex tailwind-items-center tailwind-justify-center">
              {/* Empty div for consistent spacing */}
            </div>

            {/* Center model container - Similar to horizontal mode */}
            <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-justify-center tailwind-border tailwind-border-gray-200 tailwind-rounded-md tailwind-overflow-hidden">
              <div
                className={`tailwind-w-full tailwind-flex tailwind-justify-center media-container tailwind-overflow-hidden tailwind-h-auto tailwind-p-0 tailwind-box-border tailwind-items-center tailwind-max-w-full ${
                  isEditing ? 'editing' : ''
                }`}
              >
                <div
                  className={`tailwind-relative tailwind-w-full tailwind-rounded-lg tailwind-overflow-hidden tailwind-bg-white tailwind-border tailwind-border-gray-200 tailwind-shadow-sm`}
                  style={{
                    height: '400px', // Fixed height for vertical items
                  }}
                >
                  {/* 3D Canvas */}
                  <div className="tailwind-relative tailwind-w-full tailwind-h-full tailwind-flex tailwind-justify-center tailwind-items-center">
                    <R3FModel3DViewer
                      modelUrl={model.mediaUrl || ''}
                      modelFormat={model.format || 'glb'}
                      autoRotate={config.autoRotate}
                      rotationSpeed={config.rotationSpeed}
                      onLoad={() => handleModelLoadForIndex(index)}
                      onError={() => handleModelErrorForIndex(index)}
                      onProgress={(progress) => {
                        handleModelProgressForIndex(index, progress);
                      }}
                    />

                    {/* Loading indicator with progress for each model */}
                    {modelLoadingStates[index]?.loading && (
                      <div className="tailwind-absolute tailwind-inset-0 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-bg-white tailwind-bg-opacity-70 tailwind-backdrop-blur-sm">
                        <div className="tailwind-bg-white tailwind-p-6 tailwind-rounded-lg tailwind-shadow-md tailwind-w-80">
                          <div className="tailwind-mb-2 tailwind-text-center tailwind-font-medium tailwind-text-gray-800">
                            Đang tải mô hình 3D
                          </div>
                          <Progress
                            percent={Math.round(
                              modelLoadingStates[index]?.progress || 0
                            )}
                            status={
                              (modelLoadingStates[index]?.progress || 0) >= 100
                                ? 'success'
                                : 'active'
                            }
                            strokeColor={{
                              '0%': '#108ee9',
                              '100%': '#87d068',
                            }}
                            showInfo={false}
                          />
                          <div className="tailwind-mt-2 tailwind-text-center tailwind-text-gray-500 tailwind-text-sm">
                            {(modelLoadingStates[index]?.progress || 0) < 100
                              ? 'Vui lòng đợi trong giây lát...'
                              : 'Hoàn tất!'}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Right empty space for consistent layout */}
            <div className="tailwind-flex tailwind-items-center tailwind-justify-center">
              {/* Empty div for consistent spacing */}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render model thumbnails with improved design
  const renderModelThumbnails = () => {
    if (!config.medias || config.medias.length <= 1) return null;

    return (
      <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-2 tailwind-justify-center tailwind-p-3 tailwind-overflow-x-auto tailwind-mt-4 tailwind-bg-gray-50 tailwind-rounded-lg">
        {config.medias.map((model, index) =>
          renderModelThumbnailItem(model, index)
        )}
      </div>
    );
  };

  // Render all models vertically (vertical mode)
  const renderVerticalModels = () => {
    try {
      // Check if we have models and are in vertical mode
      if (
        !config.medias ||
        config.medias.length === 0 ||
        config.displayMode !== 'vertical'
      ) {
        return null;
      }

      // Filter out any invalid models (missing URLs)
      const validModels = config.medias.filter(
        (model) => model && model.mediaUrl
      );

      if (validModels.length === 0) {
        return null;
      }

      return (
        <div className="vertical-models-container tailwind-w-full tailwind-mt-3 tailwind-max-w-full tailwind-mx-auto">
          <div className="tailwind-flex tailwind-flex-col tailwind-gap-6 tailwind-items-center">
            {validModels.map((model, index) =>
              renderModelItem(model, index, true)
            )}
          </div>
        </div>
      );
    } catch (error: any) {
      console.error('Error rendering vertical models:', error);
      return null;
    }
  };

  // Render current model info with improved UI
  const renderModelInfo = () => {
    if (!currentModel) return null;

    return (
      <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-w-full tailwind-mb-3 tailwind-p-2 tailwind-bg-gray-50 tailwind-rounded-lg">
        {/* Model name with edit functionality */}
        <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
          {editingNameIndex === selectedIndex ? (
            <div className="tailwind-flex tailwind-items-center tailwind-gap-1">
              <Input
                value={tempModelName}
                onChange={(e) => setTempModelName(e.target.value)}
                size="small"
                className="tailwind-max-w-[200px]"
                autoFocus
                onPressEnter={saveModelName}
              />
              <Button
                type="text"
                size="small"
                icon={<CheckmarkOutlineIcon />}
                onClick={saveModelName}
                style={{ color: '#52c41a' }}
              />
              <Button
                type="text"
                size="small"
                icon={<DismissOutlineIcon />}
                onClick={cancelEditingModelName}
                style={{ color: '#ff4d4f' }}
              />
            </div>
          ) : (
            <div className="tailwind-flex tailwind-items-center tailwind-gap-1">
              <span className="tailwind-text-base tailwind-font-medium">
                {getFileNameWithoutExtension(
                  currentModel.name || `Mô hình ${selectedIndex + 1}`
                )}
              </span>
              {isEditing && (
                <Tooltip title="Chỉnh sửa tên mô hình">
                  <Button
                    type="text"
                    icon={<EditIcon height={14} width={14} />}
                    onClick={() => startEditingModelName(selectedIndex)}
                    className="tailwind-text-gray-500 hover:tailwind-text-primary"
                    style={{
                      width: '26px',
                      height: '26px',
                      minWidth: '26px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: 0,
                    }}
                  />
                </Tooltip>
              )}
            </div>
          )}
        </div>

        {/* Format badge */}
        <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
          {/* Control buttons */}
          <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
            {/* Download button - always visible */}
            <Tooltip title="Tải xuống mô hình">
              <Button
                type="text"
                icon={<ArrowDownloadIcon />}
                onClick={() =>
                  currentModel && handleDownloadModel(currentModel)
                }
                style={{
                  width: '26px',
                  height: '26px',
                  minWidth: '26px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: 0,
                  backgroundColor: 'white',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px',
                }}
              />
            </Tooltip>

            {/* Convert to Base64 JS button - only for uploaded files */}
            {currentModel &&
              currentModel.mediaType === 'upload' &&
              currentModel.blobKey && (
                <Tooltip title="Convert thành file base64 JS">
                  <Button
                    type="text"
                    icon={<FileOutlineIcon />}
                    onClick={() => handleConvertToBase64JS(currentModel)}
                    style={{
                      width: '26px',
                      height: '26px',
                      minWidth: '26px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: 0,
                      backgroundColor: 'white',
                      border: '1px solid #1890ff',
                      borderRadius: '4px',
                      color: '#1890ff',
                    }}
                  />
                </Tooltip>
              )}

            {/* Delete button - only in edit mode */}
            {isEditing && (
              <Tooltip title="Xóa mô hình">
                <Button
                  type="text"
                  danger
                  icon={<DeleteIcon />}
                  onClick={() => handleDeleteModel(selectedIndex)}
                  style={{
                    width: '26px',
                    height: '26px',
                    minWidth: '26px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: 0,
                    backgroundColor: 'white',
                    border: '1px solid #ff4d4f',
                    borderRadius: '4px',
                  }}
                />
              </Tooltip>
            )}
          </div>
        </div>
      </div>
    );
  };
  // Render upload button with improved styling
  const renderUploadButton = () => {
    return (
      <MediaFileUploaderContainer
        mediaType="model3d"
        onUploadSuccess={handleUploadSuccess}
        allowDrop={false}
        onUploadComplete={() => {}}
      />
    );
  };
  // Create upload component with custom button
  const createUploadComponent = (buttonContent?: React.ReactNode) => {
    return (
      <div className="tailwind-mt-4">
        {buttonContent || renderUploadButton()}
      </div>
    );
  };

  // Render drag and drop area with improved design
  const renderDragDropArea = () => {
    return (
      <div className="tailwind-flex tailwind-flex-col tailwind-h-full tailwind-w-full">
        <MediaFileUploaderContainer
          mediaType="model3d"
          onUploadSuccess={handleUploadSuccess}
          allowDrop={true}
          dropAreaHeight="calc(100% - 40px)"
          dropAreaWidth="100%"
          className={isEditing ? 'editing' : ''}
          onUploadComplete={() => {}}
          isEditing={isEditing}
          containerRef={containerRef}
        />
      </div>
    );
  };

  // Handle display mode change
  const handleDisplayModeChange = (mode: 'horizontal' | 'vertical') => {
    addOrUpdateParamComponent({
      ...config,
      displayMode: mode,
    });
  };

  // Render configuration options
  const renderConfigOptions = () => {
    return (
      <div className="tailwind-w-full tailwind-mb-4 tailwind-bg-gray-100 tailwind-p-4 tailwind-rounded-lg">
        <div className="tailwind-flex tailwind-flex-wrap tailwind-items-center">
          {/* Display mode and basic settings in one row */}
          <div className="tailwind-flex tailwind-flex-wrap tailwind-items-start tailwind-gap-x-6 tailwind-gap-y-3 tailwind-mb-4">
            <div className="tailwind-flex tailwind-items-center">
              <span className="tailwind-font-semibold tailwind-w-40 tailwind-whitespace-nowrap">
                Chế độ hiển thị:
              </span>
              <Select
                className="tailwind-w-48 tailwind-max-w-full"
                value={config.displayMode || 'horizontal'}
                onChange={(value) =>
                  handleDisplayModeChange(value as 'horizontal' | 'vertical')
                }
                size="middle"
                popupMatchSelectWidth={false}
                style={{ minWidth: '180px' }}
              >
                <Select.Option value="horizontal">Trượt ngang</Select.Option>
                <Select.Option value="vertical">Xếp dọc</Select.Option>
              </Select>
            </div>
            <div className="tailwind-flex tailwind-items-center ">
              <span className="tailwind-font-semibold tailwind-whitespace-nowrap">
                Tốc độ xoay:
              </span>
              <div className="tailwind-w-40">
                <Slider
                  min={0.1}
                  max={5}
                  step={0.1}
                  value={tempSettings.rotationSpeed}
                  onChange={(value) => {
                    setTempSettings({
                      ...tempSettings,
                      rotationSpeed: value,
                    });
                    if (modelViewerRef.current) {
                      modelViewerRef.current.setRotationSpeed(value);
                    }
                  }}
                  tooltip={{ formatter: (value) => `${value}x` }}
                />
              </div>
            </div>

            <div className="tailwind-flex tailwind-items-center">
              <span className="tailwind-font-semibold tailwind-w-40 tailwind-whitespace-nowrap">
                Xoay tự động
              </span>
              <Switch
                checked={tempSettings.autoRotate}
                onChange={(checked) => {
                  setTempSettings({
                    ...tempSettings,
                    autoRotate: checked,
                  });
                  toggleAutoRotate();
                }}
              />
            </div>

            <div className="tailwind-flex tailwind-items-center">
              <span className="tailwind-font-semibold tailwind-w-40 tailwind-whitespace-nowrap">
                Auto convert base64 JS
              </span>
              <Switch
                checked={autoConvertToBase64JS}
                onChange={setAutoConvertToBase64JS}
                size="small"
              />
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Render the main component content
  const renderMainComponent = () => {
    const hasModels = config.medias && config.medias.length > 0;

    return (
      <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-w-full tailwind-overflow-visible tailwind-pb-4">
        {/* URL Input and Upload buttons - Always visible in edit mode */}
        {isEditing && (
          <div className="tailwind-w-full tailwind-flex tailwind-items-center tailwind-justify-between tailwind-gap-2 tailwind-mb-2">
            <div
              className="tailwind-flex tailwind-items-center tailwind-gap-2"
              style={{ width: '50%' }}
            >
              <Input
                placeholder="Nhập URL mô hình 3D (GLB, OBJ)"
                value={tempUrl}
                onChange={(e) => setTempUrl(e.target.value)}
                onPressEnter={handleUrlSubmit}
                prefix={
                  <LinkOutlinedIcon
                    height={16}
                    width={16}
                    className="tailwind-text-primary"
                  />
                }
                style={{ width: '100%' }}
                size="middle"
              />
              <Button
                onClick={handleUrlSubmit}
                className="tailwind-flex-shrink-0"
                style={{
                  backgroundColor: 'var(--edtt-color-primary)',
                  color: 'var(--edtt-color-white)',
                  border: 'none',
                  borderRadius: '4px',
                }}
              >
                Thêm mô hình
              </Button>
            </div>
            {config.medias && config.medias.length > 0 && (
              <Button
                type="primary"
                danger
                icon={<DeleteIcon />}
                onClick={() => handleDeleteModel()}
                size="middle"
              >
                Xóa tất cả mô hình
              </Button>
            )}
          </div>
        )}

        {/* Configuration options - Only visible in edit mode */}
        {isEditing && hasModels && renderConfigOptions()}

        {/* 3D Viewer Container with improved layout */}
        <div className="tailwind-w-full tailwind-flex tailwind-flex-col tailwind-items-center">
          {hasModels ? (
            <>
              {/* Upload button below the viewer when in edit mode */}
              {isEditing && (
                <>
                  {createUploadComponent(renderUploadButton())}
                  <p className="tailwind-mt-4 tailwind-text-sm tailwind-text-center tailwind-italic tailwind-text-gray-400">
                    Hoặc kéo & thả để thêm mô hình 3D
                  </p>
                </>
              )}

              {/* Main model container with navigation - horizontal or vertical based on displayMode */}
              {config.displayMode === 'vertical' ? (
                // Vertical layout - Display all models stacked
                <div
                  ref={containerRef}
                  className={`tailwind-relative tailwind-flex tailwind-flex-col tailwind-items-center tailwind-w-full tailwind-max-w-full tailwind-mx-auto ${
                    isEditing ? 'media-container editing' : ''
                  }`}
                >
                  {/* Render all models vertically */}
                  {renderVerticalModels()}
                </div>
              ) : (
                // Horizontal layout (default)
                <div className="tailwind-w-full tailwind-flex tailwind-flex-col tailwind-gap-4 tailwind-mb-3">
                  {/* Main model container with navigation */}
                  <div className="tailwind-w-full tailwind-grid tailwind-grid-cols-[60px_1fr_60px] tailwind-gap-2 tailwind-items-center">
                    {/* Left navigation button section */}
                    <div className="tailwind-flex tailwind-items-center tailwind-justify-center">
                      {config.medias && config.medias.length > 1 && (
                        <Button
                          icon={<ChevronLeftIcon />}
                          onClick={() => {
                            if (selectedIndex > 0) {
                              setSelectedIndex(selectedIndex - 1);
                            }
                          }}
                          disabled={selectedIndex === 0}
                          className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-border-0 tailwind-text-white tailwind-bg-gray-500 hover:tailwind-bg-gray-600 tailwind-w-[44px] tailwind-h-[44px] tailwind-rounded-[4px] tailwind-text-xl tailwind-shadow-md"
                        />
                      )}
                    </div>

                    {/* Center model container */}
                    <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-justify-center tailwind-border tailwind-border-gray-200 tailwind-rounded-md tailwind-overflow-hidden">
                      <DragDropMediaWrapper
                        mediaType="model3d"
                        isEditing={isEditing}
                        onUploadSuccess={handleUploadSuccess}
                        className={`tailwind-w-full tailwind-flex tailwind-justify-center media-container tailwind-overflow-hidden tailwind-h-auto tailwind-p-0 tailwind-box-border tailwind-items-center tailwind-max-w-full ${
                          isEditing ? 'editing' : ''
                        }`}
                      >
                        <div
                          ref={containerRef}
                          className={`tailwind-relative tailwind-w-full tailwind-rounded-lg tailwind-overflow-hidden tailwind-bg-white tailwind-border tailwind-border-gray-200 tailwind-shadow-sm`}
                          style={{
                            height:
                              typeof config.height === 'number'
                                ? `${config.height}px`
                                : config.height || '600px',
                          }}
                        >
                          {/* Current model info */}
                          {renderModelInfo()}

                          {/* 3D Canvas */}
                          <div className="tailwind-relative tailwind-w-full tailwind-h-full tailwind-flex tailwind-justify-center tailwind-items-center">
                            <R3FModel3DViewer
                              ref={modelViewerRef}
                              modelUrl={currentModel?.mediaUrl || ''}
                              modelFormat={currentModel?.format || 'glb'}
                              autoRotate={config.autoRotate}
                              rotationSpeed={config.rotationSpeed}
                              onLoad={handleModelLoad}
                              onError={handleModelError}
                              onProgress={handleModelProgress}
                            />

                            {/* Enhanced Loading indicator with progress */}
                            {isLoading && (
                              <div className="tailwind-absolute tailwind-inset-0 tailwind-flex tailwind-flex-col tailwind-items-center tailwind-justify-center tailwind-bg-white tailwind-bg-opacity-70 tailwind-backdrop-blur-sm">
                                <div className="tailwind-bg-white tailwind-p-6 tailwind-rounded-lg tailwind-shadow-md tailwind-w-80">
                                  <div className="tailwind-mb-2 tailwind-text-center tailwind-font-medium tailwind-text-gray-800">
                                    Đang tải mô hình 3D
                                  </div>
                                  <Progress
                                    percent={Math.round(loadProgress)}
                                    status={
                                      loadProgress >= 100 ? 'success' : 'active'
                                    }
                                    strokeColor={{
                                      '0%': '#108ee9',
                                      '100%': '#87d068',
                                    }}
                                    showInfo={false}
                                  />
                                  <div className="tailwind-mt-2 tailwind-text-center tailwind-text-gray-500 tailwind-text-sm">
                                    {loadProgress < 100
                                      ? 'Vui lòng đợi trong giây lát...'
                                      : 'Hoàn tất!'}
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </DragDropMediaWrapper>
                    </div>

                    {/* Right navigation button section */}
                    <div className="tailwind-flex tailwind-items-center tailwind-justify-center">
                      {config.medias && config.medias.length > 1 && (
                        <Button
                          icon={<ChevronRightIcon />}
                          onClick={() => {
                            if (
                              config.medias &&
                              selectedIndex < config.medias.length - 1
                            ) {
                              setSelectedIndex(selectedIndex + 1);
                            }
                          }}
                          disabled={
                            !config.medias ||
                            selectedIndex === config.medias.length - 1
                          }
                          className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-border-0 tailwind-text-white tailwind-bg-gray-500 hover:tailwind-bg-gray-600 tailwind-w-[44px] tailwind-h-[44px] tailwind-rounded-[4px] tailwind-text-xl tailwind-shadow-md"
                        />
                      )}
                    </div>
                  </div>

                  {/* Model thumbnails below the viewer in horizontal mode */}
                  {renderModelThumbnails()}
                </div>
              )}
            </>
          ) : isEditing ? (
            // If no models and in edit mode, show upload area
            <div
              ref={containerRef}
              className={`tailwind-relative tailwind-flex tailwind-flex-col tailwind-items-center tailwind-w-full tailwind-max-w-full tailwind-min-h-[200px] media-file-uploader-container media-container editing`}
            >
              {renderDragDropArea()}
            </div>
          ) : (
            // If no models and not in edit mode, show empty state
            <div className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-h-[600px] tailwind-w-full tailwind-p-4 tailwind-border tailwind-border-gray-200 tailwind-rounded-lg">
              <div className="tailwind-text-center tailwind-text-gray-500">
                <p>Không có mô hình 3D nào được thêm vào.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <EngineContainer
      node={props}
      mainComponent={renderMainComponent()}
      allowConfiguration={false}
      showFullscreenButton={true}
      id={props.path}
      titleProps={config.titleProps}
      onTitleUpdate={handleTitleUpdate}
      instructionsContent={config.instructionsContent}
      className="tailwind-overflow-visible tailwind-pb-4" // Allow content to be visible and add padding
    />
  );
};

export default withEdComponentParams(Model3DComponent);
