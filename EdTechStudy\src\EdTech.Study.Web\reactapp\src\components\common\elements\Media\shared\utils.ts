import { appConfig } from '../../../../../constants/AppContants';

/**
 * Process a URL to ensure it's properly formatted for loading resources
 * @param url The input URL to process
 * @param options Optional configuration options
 * @returns The processed URL
 */
export const processResourceUrl = (
  url: string,
  options: {
    addOrigin?: boolean;
    encodeSpecialChars?: boolean;
    corsProxy?: string;
    forceHttps?: boolean;
  } = {
    addOrigin: true,
    encodeSpecialChars: true,
    corsProxy: '',
    forceHttps: false,
  }
): string => {
  if (!url) return url;

  let processedUrl = url;

  // Handle relative URLs - ensure they start with a slash
  if (
    !processedUrl.startsWith('http') &&
    !processedUrl.startsWith('data:') &&
    !processedUrl.startsWith('/')
  ) {
    processedUrl = '/' + processedUrl;
  }

  // Add origin for relative URLs if needed
  if (
    options.addOrigin &&
    !processedUrl.startsWith('http') &&
    !processedUrl.startsWith('data:')
  ) {
    // Use our constant for the base URL instead of window.location.origin
    processedUrl = appConfig.baseURL + processedUrl;
  }

  // Force HTTPS if requested
  if (options.forceHttps && processedUrl.startsWith('http:')) {
    processedUrl = processedUrl.replace(/^http:/, 'https:');
  }

  // Handle URL encoding for special characters if needed
  if (
    options.encodeSpecialChars &&
    (processedUrl.includes(' ') || /[^\x00-\x7F]/.test(processedUrl))
  ) {
    try {
      const urlObj = new URL(processedUrl);
      processedUrl = urlObj.toString();
    } catch (error) {
      // Try to encode manually if URL constructor fails
      processedUrl = encodeURI(processedUrl);
    }
  }

  // Add CORS proxy if provided
  if (options.corsProxy && processedUrl.startsWith('http')) {
    processedUrl = `${options.corsProxy}${encodeURIComponent(processedUrl)}`;
  }

  return processedUrl;
};

/**
 * Process a media URL for any media type (image, video, audio, 3D model)
 * @param url The input URL to process
 * @param mediaType Optional media type for specific processing
 * @returns An object containing the full URL and original URL
 */
export const processMediaUrl = (
  url: string,
  mediaType?: 'image' | 'video' | 'audio' | 'model3d'
): { fullUrl: string; originalUrl: string } => {
  // Ensure URL starts with a slash if it's a relative URL
  let mediaUrl = url;
  if (
    mediaUrl &&
    !mediaUrl.startsWith('/') &&
    !mediaUrl.startsWith('http') &&
    !mediaUrl.startsWith('data:')
  ) {
    mediaUrl = '/' + mediaUrl;
  }

  // Process the URL with our utility function
  const fullUrl = processResourceUrl(mediaUrl, {
    addOrigin: true,
    encodeSpecialChars: true,
    // Force HTTPS for video and audio to avoid mixed content issues
    forceHttps: mediaType === 'video' || mediaType === 'audio',
  });

  return { fullUrl, originalUrl: mediaUrl };
};

/**
 * Process a model URL (alias for processMediaUrl with model3d type)
 * @param url The input URL to process
 * @returns An object containing the full URL and original URL
 */
export const processModelUrl = (
  url: string
): { fullUrl: string; originalUrl: string } => {
  return processMediaUrl(url, 'model3d');
};

/**
 * Process an image URL (alias for processMediaUrl with image type)
 * @param url The input URL to process
 * @returns An object containing the full URL and original URL
 */
export const processImageUrl = (
  url: string
): { fullUrl: string; originalUrl: string } => {
  return processMediaUrl(url, 'image');
};

/**
 * Process a video URL (alias for processMediaUrl with video type)
 * @param url The input URL to process
 * @returns An object containing the full URL and original URL
 */
export const processVideoUrl = (
  url: string
): { fullUrl: string; originalUrl: string } => {
  return processMediaUrl(url, 'video');
};

/**
 * Process an audio URL (alias for processMediaUrl with audio type)
 * @param url The input URL to process
 * @returns An object containing the full URL and original URL
 */
export const processAudioUrl = (
  url: string
): { fullUrl: string; originalUrl: string } => {
  return processMediaUrl(url, 'audio');
};

/**
 * Check if a URL is likely to have CORS issues
 * @param url The URL to check
 * @returns True if the URL might have CORS issues
 */
export const mightHaveCorsIssues = (url: string): boolean => {
  if (!url.startsWith('http')) return false;

  try {
    const urlObj = new URL(url);
    const urlOrigin = urlObj.origin;

    // Check if the URL origin matches our API base URL
    return !urlOrigin.startsWith(appConfig.baseURL);
  } catch (error) {
    return false;
  }
};

/**
 * Extract file extension from a URL or filename
 * @param url The URL or filename to analyze
 * @returns The file extension without the dot, or empty string if none found
 */
export const getFileExtension = (url: string): string => {
  if (!url) return '';

  // Remove query parameters and hash
  const cleanUrl = url.split(/[?#]/)[0];

  // Get the last part after the last dot
  const extension = cleanUrl.split('.').pop()?.toLowerCase() || '';

  // If the entire string was returned (no dots in the name), return empty string
  if (extension === cleanUrl.toLowerCase()) {
    return '';
  }

  return extension;
};

/**
 * Get file name without extension from a URL or filename
 * @param url The URL or filename to analyze
 * @returns The filename without extension
 */
export const getFileNameWithoutExtension = (url: string): string => {
  if (!url) return '';

  // Get the filename from the URL (last part after the last slash)
  const filename = url.split('/').pop() || '';

  // Remove the extension
  const lastDotIndex = filename.lastIndexOf('.');
  return lastDotIndex > 0 ? filename.substring(0, lastDotIndex) : filename;
};

/**
 * Guess the format of a 3D model from its URL
 * @param url The URL to analyze
 * @returns The guessed format or null if can't determine
 */
export const guessFormatFromUrl = (
  url: string
): 'obj' | 'glb' | 'fbx' | null => {
  const extension = getFileExtension(url);

  // Check for known extensions
  if (extension === 'obj') return 'obj';
  if (extension === 'glb') return 'glb';
  if (extension === 'fbx') return 'fbx';

  // Try to guess from URL patterns
  const lowerUrl = url.toLowerCase();
  if (lowerUrl.includes('/obj/') || lowerUrl.includes('_obj_')) return 'obj';
  if (lowerUrl.includes('/glb/') || lowerUrl.includes('_glb_')) return 'glb';
  if (lowerUrl.includes('/fbx/') || lowerUrl.includes('_fbx_')) return 'fbx';

  // Can't determine format
  return null;
};

/**
 * Extract YouTube video ID from a YouTube URL
 * @param url The YouTube URL
 * @returns The YouTube video ID or empty string if not found
 */
export const extractYoutubeId = (url: string): string => {
  const regExp =
    /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
  const match = url.match(regExp);
  return match && match[2].length === 11 ? match[2] : '';
};

/**
 * Extract Vimeo video ID from a Vimeo URL
 * @param url The Vimeo URL
 * @returns The Vimeo video ID or empty string if not found
 */
export const extractVimeoId = (url: string): string => {
  const regExp =
    /vimeo\.com\/(?:channels\/(?:\w+\/)?|groups\/(?:[^\/]*)\/videos\/|album\/(?:\d+)\/video\/|)(\d+)(?:$|\/|\?)/;
  const match = url.match(regExp);
  return match ? match[1] : '';
};

/**
 * Convert a file to base64 string
 * @param file The file to convert
 * @returns Promise<string> The base64 string
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        // Remove the data URL prefix (e.g., "data:application/octet-stream;base64,")
        const base64 = reader.result.split(',')[1];
        resolve(base64);
      } else {
        reject(new Error('Failed to convert file to base64'));
      }
    };
    reader.onerror = () => reject(new Error('Error reading file'));
    reader.readAsDataURL(file);
  });
};

/**
 * Create base64 JavaScript content
 * @param fileName The original file name
 * @param base64Content The base64 content
 * @param originalFile The original file for metadata
 * @returns The JavaScript content as string
 */
export const createBase64JSContent = (
  fileName: string,
  base64Content: string,
  originalFile: File
): string => {
  // Get file name without extension
  const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '');

  // Create JavaScript content
  const jsContent = `// Base64 encoded 3D model: ${fileName}
// Original file size: ${originalFile.size} bytes
// MIME type: ${originalFile.type}
// Generated on: ${new Date().toISOString()}

const ${nameWithoutExt.replace(
    /[^a-zA-Z0-9]/g,
    '_'
  )}_base64 = "${base64Content}";

export default ${nameWithoutExt.replace(/[^a-zA-Z0-9]/g, '_')}_base64;
`;

  return jsContent;
};

/**
 * Create and download a JavaScript file with base64 content
 * @param fileName The original file name
 * @param base64Content The base64 content
 * @param originalFile The original file for metadata
 */
export const createAndDownloadBase64JSFile = (
  fileName: string,
  base64Content: string,
  originalFile: File
) => {
  // Get file name without extension
  const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '');

  // Create JavaScript content using the shared function
  const jsContent = createBase64JSContent(
    fileName,
    base64Content,
    originalFile
  );

  // Create blob and download
  const blob = new Blob([jsContent], { type: 'application/javascript' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${nameWithoutExt}.b64.js`;

  // Trigger download
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  // Clean up
  URL.revokeObjectURL(url);
};

/**
 * Process 3D model files to base64 and create JS files
 * @param files Array of 3D model files
 * @returns Promise<void>
 */
export const process3DFilesToBase64JS = async (
  files: File[]
): Promise<void> => {
  for (const file of files) {
    try {
      const base64Content = await fileToBase64(file);
      createAndDownloadBase64JSFile(file.name, base64Content, file);
    } catch (error) {
      console.error(`Error processing file ${file.name}:`, error);
      throw error;
    }
  }
};
